<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <link rel="stylesheet" href="style.css" />
  <title>To-Do List - Task Manager</title>
</head>
<body>
<div class="container">
  <div class="todo-header">
    <h2>My To-Do List</h2>
    <div class="user-info">
      <span id="welcomeUser">Welcome!</span>
      <button id="logoutBtn" class="logout-btn">Logout</button>
    </div>
  </div>
  
  <form id="todoForm" class="form">
    <div class="form-control">
      <label for="taskTitle">Task Title</label>
      <input type="text" id="taskTitle" placeholder="Enter task title" />
      <small>Error message</small>
    </div>
    <div class="form-control">
      <label for="taskDescription">Description (Optional)</label>
      <textarea id="taskDescription" placeholder="Enter task description" rows="3"></textarea>
      <small>Error message</small>
    </div>
    <div class="form-control">
      <label for="taskPriority">Priority</label>
      <select id="taskPriority">
        <option value="low">Low</option>
        <option value="medium" selected>Medium</option>
        <option value="high">High</option>
      </select>
      <small>Error message</small>
    </div>
    <button type="submit" id="addTaskBtn">Add Task</button>
    <button type="button" id="cancelEditBtn" class="cancel-btn" style="display: none;">Cancel Edit</button>
  </form>

  <div class="todo-filters">
    <button class="filter-btn active" data-filter="all">All</button>
    <button class="filter-btn" data-filter="pending">Pending</button>
    <button class="filter-btn" data-filter="completed">Completed</button>
    <button class="filter-btn" data-filter="high">High Priority</button>
  </div>

  <div class="todo-stats">
    <span id="totalTasks">Total: 0</span>
    <span id="pendingTasks">Pending: 0</span>
    <span id="completedTasks">Completed: 0</span>
  </div>

  <div id="todoList" class="todo-list">
    <!-- Tasks will be dynamically added here -->
  </div>

  <div id="emptyState" class="empty-state">
    <p>No tasks yet. Add your first task above!</p>
  </div>
</div>

<script src="todo.js"></script>
</body>
</html>
