const form = document.getElementById('form');
const email = document.getElementById('email');
const password = document.getElementById('password');

/*
📚 EXPLANATION - DOM Element Selection for Login:

🎯 Purpose: Get references to HTML form elements for login validation

🔍 Elements Selected:
- form: The main form container element
- email: Email input field
- password: Password input field

💡 Key Concepts:
- Simplified element selection for login (no username or password confirmation)
- Reuses same validation functions from register script
- Maintains consistency with register page structure
*/

//Show input error message
function showError(input, message) {
    const formControl = input.parentElement;
    formControl.className = 'form-control error';
    const small = formControl.querySelector('small');
    small.innerText = message;
}

//Show success outline
function showSuccess(input) {
    const formControl = input.parentElement;
    formControl.className = 'form-control success';
}

//Check email is valid
function isValidEmail(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

//Get field name
function getFieldName(input) {
    return input.id.charAt(0).toUpperCase() + input.id.slice(1);
}

//Check required fields
function checkRequired(inputArr) {
    inputArr.forEach(function (input) {
        if (input.value.trim() === '') {
            showError(input, `${getFieldName(input)} is required`);
        } else {
            showSuccess(input);
        }
    });
}

//Check email
function checkEmail(input) {
    if (isValidEmail(input.value)) {
        showSuccess(input);
    } else {
        showError(input, 'Email is not valid');
    }
}

//Check input length (simplified for login - just minimum length)
function checkMinLength(input, min) {
    if (input.value.length < min) {
        showError(input, `${getFieldName(input)} must be at least ${min} characters`);
    } else {
        showSuccess(input);
    }
}

/*
📚 EXPLANATION - Login Validation Functions:

🎯 Purpose: Reuse validation logic from register page with minimal changes

🔧 Functions Included:
- showError(): Display error styling and message
- showSuccess(): Display success styling
- isValidEmail(): Validate email format using regex
- getFieldName(): Convert input ID to user-friendly name
- checkRequired(): Ensure fields are not empty
- checkEmail(): Validate email format and show feedback
- checkMinLength(): Simplified length check for login

💡 Key Differences from Register:
- No username validation needed
- No password confirmation needed
- Simplified length check (only minimum, no maximum for login)
- Fewer validation calls in submit handler
*/

//Event Listeners
form.addEventListener('submit', (e) => {
    e.preventDefault();

    // Run validation checks for login
    checkRequired([email, password]);
    checkEmail(email);
    checkMinLength(password, 6);

    // Check if all validations passed
    const formControls = document.querySelectorAll('.form-control');
    let isValid = true;
    
    formControls.forEach(control => {
        if (control.classList.contains('error')) {
            isValid = false;
        }
    });

    // If all validations pass, you could redirect or submit to server
    if (isValid) {
        console.log('Login form is valid!');
        // Here you would typically send the data to your server
        // For demo purposes, just show an alert
        alert('Login successful! (This is just a demo)');
    }
});

/*
📚 EXPLANATION - Login Form Submit Handler:

🎯 Purpose: Handle login form submission with simplified validation

⚙️ How It Works:
1. Prevents default form submission
2. Runs required field validation
3. Validates email format
4. Checks minimum password length
5. Checks if all validations passed
6. Provides feedback or processes login

🔍 Validation Sequence:
1. checkRequired([email, password]): Ensures both fields have values
2. checkEmail(email): Validates email format
3. checkMinLength(password, 6): Ensures password is at least 6 characters

💡 Login vs Register Differences:
- No username validation
- No password confirmation
- No maximum length restrictions (for login convenience)
- Added success handling logic
- Could integrate with authentication system

🎯 Success Handling:
- Checks all form controls for error class
- Only proceeds if no errors found
- Shows demo alert (replace with actual login logic)
- Could redirect to dashboard or main application
*/
