@import url('https://fonts.googleapis.com/css?family=Open+Sans&display=swap');

:root {
    --success-color: #2ecc71;
    --error-color: #e74c3c;
}

* {
    box-sizing: border-box;
}

body {
    background-color: #f9fafb;
    font-family: 'Open Sans', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    margin: 0;
}

.container {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    width: 400px;
    max-width: 90vw;
}

/* Wider container for todo page */
body:has(#todoList) .container {
    width: 800px;
    max-width: 95vw;
}

h2 {
    text-align: center;
    margin: 0 0 20px;
}

.form {
    padding: 30px 40px;
}

.form-control {
    margin-bottom: 10px;
    padding-bottom: 20px;
    position: relative;
}

.form-control label {
    color: #777;
    display: block;
    margin-bottom: 5px;
}

.form-control input {
    border: 2px solid #f0f0f0;
    border-radius: 4px;
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 14px;
}

.form-control input:focus {
    outline: 0;
    border-color: #777;
}

.form-control.success input {
    border-color: var(--success-color);
}

.form-control.error input {
    border-color: var(--error-color);
}

.form-control small {
    color: var(--error-color);
    position: absolute;
    bottom: 0;
    left: 0;
    visibility: hidden;
}

.form-control.error small {
    visibility: visible;
}

.form button {
    cursor: pointer;
    background-color: #3498db;
    border: 2px solid #3498db;
    border-radius: 4px;
    color: #fff;
    display: block;
    font-size: 16px;
    padding: 10px;
    margin-top: 20px;
    width: 100%;
}

.form-links {
    text-align: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.form-links p {
    margin: 0;
    color: #777;
    font-size: 14px;
}

.form-links a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.form-links a:hover {
    text-decoration: underline;
}

/* To-Do List Styles */
.todo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 14px;
    color: #777;
}

.logout-btn {
    background-color: #e74c3c;
    border: 2px solid #e74c3c;
    color: white;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.logout-btn:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

.form-control textarea {
    border: 2px solid #f0f0f0;
    border-radius: 4px;
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 14px;
    font-family: 'Open Sans', sans-serif;
    resize: vertical;
    min-height: 60px;
}

.form-control select {
    border: 2px solid #f0f0f0;
    border-radius: 4px;
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
}

.form-control textarea:focus,
.form-control select:focus {
    outline: 0;
    border-color: #777;
}

.cancel-btn {
    background-color: #95a5a6;
    border: 2px solid #95a5a6;
    margin-left: 10px;
}

.todo-filters {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    justify-content: center;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #3498db;
    background-color: white;
    color: #3498db;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background-color: #3498db;
    color: white;
}

.todo-stats {
    display: flex;
    justify-content: space-around;
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 14px;
    color: #777;
}

.todo-list {
    max-height: 400px;
    overflow-y: auto;
    margin-top: 20px;
}

.task-item {
    background-color: white;
    border: 2px solid #f0f0f0;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.task-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 5px rgba(52, 152, 219, 0.1);
}

.task-item.completed {
    opacity: 0.7;
    background-color: #f8f9fa;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.task-title {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
}

.task-item.completed .task-title {
    text-decoration: line-through;
    color: #95a5a6;
}

.task-priority {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-high {
    background-color: #e74c3c;
    color: white;
}

.priority-medium {
    background-color: #f39c12;
    color: white;
}

.priority-low {
    background-color: #2ecc71;
    color: white;
}

.task-description {
    color: #777;
    font-size: 14px;
    margin: 8px 0;
    line-height: 1.4;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #95a5a6;
    margin-top: 10px;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.task-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.complete-btn {
    background-color: #2ecc71;
    color: white;
}

.complete-btn:hover {
    background-color: #27ae60;
}

.edit-btn {
    background-color: #3498db;
    color: white;
}

.edit-btn:hover {
    background-color: #2980b9;
}

.delete-btn {
    background-color: #e74c3c;
    color: white;
}

.delete-btn:hover {
    background-color: #c0392b;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #95a5a6;
    font-style: italic;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
}
