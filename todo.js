// Task Class - Represents a single task
class Task {
    constructor(title, description = '', priority = 'medium') {
        this.id = this.generateId();
        this.title = title;
        this.description = description;
        this.priority = priority;
        this.completed = false;
        this.createdAt = new Date().toISOString();
        this.updatedAt = new Date().toISOString();
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    markComplete() {
        this.completed = true;
        this.updatedAt = new Date().toISOString();
    }

    markIncomplete() {
        this.completed = false;
        this.updatedAt = new Date().toISOString();
    }

    update(title, description, priority) {
        this.title = title;
        this.description = description;
        this.priority = priority;
        this.updatedAt = new Date().toISOString();
    }

    getFormattedDate() {
        return new Date(this.createdAt).toLocaleDateString();
    }
}

// TodoManager Class - Handles CRUD operations
class TodoManager {
    constructor() {
        this.tasks = [];
        this.currentUser = localStorage.getItem('currentUser');
        this.storageKey = `todos_${this.currentUser}`;
        this.loadTasks();
    }

    // CREATE - Add new task
    addTask(title, description, priority) {
        const task = new Task(title, description, priority);
        this.tasks.push(task);
        this.saveTasks();
        return task;
    }

    // READ - Get all tasks or filtered tasks
    getTasks(filter = 'all') {
        switch (filter) {
            case 'completed':
                return this.tasks.filter(task => task.completed);
            case 'pending':
                return this.tasks.filter(task => !task.completed);
            case 'high':
                return this.tasks.filter(task => task.priority === 'high');
            default:
                return this.tasks;
        }
    }

    // READ - Get task by ID
    getTaskById(id) {
        return this.tasks.find(task => task.id === id);
    }

    // UPDATE - Update existing task
    updateTask(id, title, description, priority) {
        const task = this.getTaskById(id);
        if (task) {
            task.update(title, description, priority);
            this.saveTasks();
            return task;
        }
        return null;
    }

    // UPDATE - Toggle task completion
    toggleTaskCompletion(id) {
        const task = this.getTaskById(id);
        if (task) {
            if (task.completed) {
                task.markIncomplete();
            } else {
                task.markComplete();
            }
            this.saveTasks();
            return task;
        }
        return null;
    }

    // DELETE - Remove task
    deleteTask(id) {
        const index = this.tasks.findIndex(task => task.id === id);
        if (index !== -1) {
            const deletedTask = this.tasks.splice(index, 1)[0];
            this.saveTasks();
            return deletedTask;
        }
        return null;
    }

    // Get statistics
    getStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(task => task.completed).length;
        const pending = total - completed;
        return { total, completed, pending };
    }

    // Save tasks to localStorage
    saveTasks() {
        localStorage.setItem(this.storageKey, JSON.stringify(this.tasks));
    }

    // Load tasks from localStorage
    loadTasks() {
        const savedTasks = localStorage.getItem(this.storageKey);
        if (savedTasks) {
            const taskData = JSON.parse(savedTasks);
            this.tasks = taskData.map(data => {
                const task = new Task(data.title, data.description, data.priority);
                task.id = data.id;
                task.completed = data.completed;
                task.createdAt = data.createdAt;
                task.updatedAt = data.updatedAt;
                return task;
            });
        }
    }
}

// TodoUI Class - Handles user interface
class TodoUI {
    constructor(todoManager) {
        this.todoManager = todoManager;
        this.currentFilter = 'all';
        this.editingTaskId = null;
        this.initializeElements();
        this.attachEventListeners();
        this.displayWelcomeMessage();
        this.render();
    }

    initializeElements() {
        this.todoForm = document.getElementById('todoForm');
        this.taskTitle = document.getElementById('taskTitle');
        this.taskDescription = document.getElementById('taskDescription');
        this.taskPriority = document.getElementById('taskPriority');
        this.addTaskBtn = document.getElementById('addTaskBtn');
        this.cancelEditBtn = document.getElementById('cancelEditBtn');
        this.todoList = document.getElementById('todoList');
        this.emptyState = document.getElementById('emptyState');
        this.welcomeUser = document.getElementById('welcomeUser');
        this.logoutBtn = document.getElementById('logoutBtn');
        this.filterBtns = document.querySelectorAll('.filter-btn');
        this.totalTasks = document.getElementById('totalTasks');
        this.pendingTasks = document.getElementById('pendingTasks');
        this.completedTasks = document.getElementById('completedTasks');
    }

    attachEventListeners() {
        this.todoForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
        this.cancelEditBtn.addEventListener('click', () => this.cancelEdit());
        this.logoutBtn.addEventListener('click', () => this.handleLogout());
        
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleFilterChange(e));
        });
    }

    displayWelcomeMessage() {
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
            this.welcomeUser.textContent = `Welcome, ${currentUser}!`;
        }
    }

    handleFormSubmit(e) {
        e.preventDefault();
        
        const title = this.taskTitle.value.trim();
        const description = this.taskDescription.value.trim();
        const priority = this.taskPriority.value;

        if (!title) {
            this.showError(this.taskTitle, 'Task title is required');
            return;
        }

        if (this.editingTaskId) {
            // Update existing task
            this.todoManager.updateTask(this.editingTaskId, title, description, priority);
            this.cancelEdit();
        } else {
            // Add new task
            this.todoManager.addTask(title, description, priority);
        }

        this.clearForm();
        this.render();
    }

    showError(input, message) {
        const formControl = input.parentElement;
        formControl.className = 'form-control error';
        const small = formControl.querySelector('small');
        small.innerText = message;
    }

    clearError(input) {
        const formControl = input.parentElement;
        formControl.className = 'form-control';
    }

    clearForm() {
        this.todoForm.reset();
        this.taskPriority.value = 'medium';
        this.clearError(this.taskTitle);
        this.clearError(this.taskDescription);
    }

    startEdit(taskId) {
        const task = this.todoManager.getTaskById(taskId);
        if (task) {
            this.editingTaskId = taskId;
            this.taskTitle.value = task.title;
            this.taskDescription.value = task.description;
            this.taskPriority.value = task.priority;
            this.addTaskBtn.textContent = 'Update Task';
            this.cancelEditBtn.style.display = 'inline-block';
        }
    }

    cancelEdit() {
        this.editingTaskId = null;
        this.addTaskBtn.textContent = 'Add Task';
        this.cancelEditBtn.style.display = 'none';
        this.clearForm();
    }

    handleFilterChange(e) {
        this.filterBtns.forEach(btn => btn.classList.remove('active'));
        e.target.classList.add('active');
        this.currentFilter = e.target.dataset.filter;
        this.render();
    }

    handleLogout() {
        localStorage.removeItem('currentUser');
        window.location.href = 'login.html';
    }

    render() {
        this.renderTasks();
        this.renderStats();
    }

    renderTasks() {
        const tasks = this.todoManager.getTasks(this.currentFilter);
        
        if (tasks.length === 0) {
            this.todoList.style.display = 'none';
            this.emptyState.style.display = 'block';
            return;
        }

        this.todoList.style.display = 'block';
        this.emptyState.style.display = 'none';
        
        this.todoList.innerHTML = tasks.map(task => this.createTaskHTML(task)).join('');
        
        // Attach event listeners to task buttons
        this.attachTaskEventListeners();
    }

    createTaskHTML(task) {
        return `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-task-id="${task.id}">
                <div class="task-header">
                    <div class="task-title">${task.title}</div>
                    <div class="task-priority priority-${task.priority}">${task.priority}</div>
                </div>
                ${task.description ? `<div class="task-description">${task.description}</div>` : ''}
                <div class="task-meta">
                    <span>Created: ${task.getFormattedDate()}</span>
                    <div class="task-actions">
                        <button class="task-btn complete-btn" data-action="toggle" data-task-id="${task.id}">
                            ${task.completed ? 'Undo' : 'Complete'}
                        </button>
                        <button class="task-btn edit-btn" data-action="edit" data-task-id="${task.id}">Edit</button>
                        <button class="task-btn delete-btn" data-action="delete" data-task-id="${task.id}">Delete</button>
                    </div>
                </div>
            </div>
        `;
    }

    attachTaskEventListeners() {
        const taskBtns = this.todoList.querySelectorAll('.task-btn');
        taskBtns.forEach(btn => {
            btn.addEventListener('click', (e) => this.handleTaskAction(e));
        });
    }

    handleTaskAction(e) {
        const action = e.target.dataset.action;
        const taskId = e.target.dataset.taskId;

        switch (action) {
            case 'toggle':
                this.todoManager.toggleTaskCompletion(taskId);
                this.render();
                break;
            case 'edit':
                this.startEdit(taskId);
                break;
            case 'delete':
                if (confirm('Are you sure you want to delete this task?')) {
                    this.todoManager.deleteTask(taskId);
                    this.render();
                }
                break;
        }
    }

    renderStats() {
        const stats = this.todoManager.getStats();
        this.totalTasks.textContent = `Total: ${stats.total}`;
        this.pendingTasks.textContent = `Pending: ${stats.pending}`;
        this.completedTasks.textContent = `Completed: ${stats.completed}`;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    // Check if user is logged in
    const currentUser = localStorage.getItem('currentUser');
    if (!currentUser) {
        window.location.href = 'login.html';
        return;
    }

    // Initialize TodoManager and TodoUI
    const todoManager = new TodoManager();
    const todoUI = new TodoUI(todoManager);
});
